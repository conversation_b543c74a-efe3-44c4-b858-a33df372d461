/********************************************************************************
 * ChestMgr
 * author: cloud
 * date: 2025-01-21
 ********************************************************************************/
#pragma once

#include <vector>
#include <random>
#include <chrono>
#include <string>
#include "DefManagerProxy.h"
#include "SandboxEngine.h"

class World;
class WorldContainerMgr;

struct EXPORT_SANDBOXENGINE SocBuildingChest {
    WCoord pos;
    int chestId;
    int blockId;
    unsigned short spawnCount;
    unsigned short deadDay;
    bool isSpawned;
};

struct EXPORT_SANDBOXENGINE SocChestInfo {
	WCoord pos;
	const ChestSpawnDef* config;
	int chestId;
    unsigned short spawnCount;
    unsigned short deadDay;
	bool isSpawned;
};

class EXPORT_SANDBOXENGINE ChestManager {
public:

    ChestManager(World* world, int mapSize = 6000);
    ~ChestManager();

    void tick();

    bool initRandomChestsInfo();
    bool initBuildingChestsInfo();

    void onChunkLoaded(int chunkX, int chunkZ);
    void onChunkUnloaded(int chunkX, int chunkZ);
    void onDestroyContainer(const WCoord& pos);

    bool addBuildingChest(int blockId, const WCoord& pos);
    bool saveBuildingChestsToJson(const std::string& filePath = "");

    const std::vector<SocChestInfo>& getChests() const;

private:
	
    bool isValidChestPosition(const WCoord& pos) const;
    bool spawnChest(const SocChestInfo& chestInfo, int& err);
    bool spawnBuildingChest(SocBuildingChest& chestInfo, int& err);
    
    std::string getTerrainTypeAtPosition(const WCoord& pos) const;
    bool isChunkLoaded(int chunkX, int chunkZ) const;
    void trySpawnChestsInChunk(int chunkX, int chunkZ);
    int generateBuildingChestId(int blockId, int chunkX, int chunkZ);

    void refreshFixedTimeChest(const ChestSpawnDef& def, int gameTimeInMinutes);
    
    void clearBuildingChests();
    void refreshBuildingChests(int gameday);

    void dumpChests();

private:
    World* m_world;
    WorldContainerMgr* m_containerMgr;
    std::unordered_map<WCoord, SocChestInfo, WCoordHashCoder> m_RandomChests;
    std::unordered_map<WCoord, SocBuildingChest, WCoordHashCoder> m_BuildingChests;
    std::map<unsigned int, SocBuildingChest> m_BuildingChestSpawnInfo;
    mutable std::vector<SocChestInfo> m_AllChests;  // 用于返回所有宝箱信息
    const int m_mapSize;
    std::chrono::steady_clock::time_point m_lastRefreshTime;  // 上次完全刷新的时间
    std::chrono::steady_clock::time_point m_lastTimeCheckTime; // 上次检查游戏时间的时间
    bool m_fixedTimeChestHasRefresh;
    std::mt19937 m_randGen;
    int m_lastWorldTime;
    int m_lastGameDay;  // 记录上次检查时的游戏天数
    std::string m_buildingChestConfigPath;
};